const excitingTweets = [
    // General Introductory Tweets
    "🚀 Meet AskZen AI, the chatbot that answers your website visitors’ questions in real-time—without coding! Launching 1 Jan 2025. Stay tuned! 💬✨ #AIChatbot #WebsiteSupport #LaunchSoon #AskZenAI",
    "What if your website could answer all visitor questions—instantly? Enter AskZen AI! Launching soon. Follow us for updates. 🤖✨ #AI #TechInnovation #WebsiteChatbot #AskZenAI",
    "Supercharge your website’s customer support with AskZen AI. 🚀 It’s simple: copy a script tag, and you're live. Launching Jan 1st, 2025! 🗓️ #NoCode #Chatbots #WebSupport #AskZenAI",
    "Imagine having a 24/7 assistant for your website visitors. AskZen AI makes it real! Launching Jan 2025. #WebsiteSupport #AIChatbot #NoCode #AskZenAI",
    "Got FAQs? Let AskZen AI handle them while you focus on growing your business. Launching soon! 🚀 #AIChatbots #Automation #TechInnovation #AskZenAI",
  
    // Features and Benefits
    "🤖 AskZen AI = No more long FAQs. Embed, enable, and let your website do the talking! Launching 2025. 🔥 #AskZenAI #Automation #WebsiteChatbot",
    "🌟 No coding? No problem! With AskZen AI, embed our chatbot with a single script. Simplifying support like never before! 💡 #NoCode #AIChatbots #Innovation #AskZenAI",
    "🚀 Increase conversions with a 24/7 smart assistant on your website. AskZen AI is launching soon! 🛠️ #ConversionBoost #AIForWeb #WebsiteSupport #AskZenAI",
    "Your website deserves an upgrade. Embed AskZen AI and answer questions instantly. Coming Jan 2025! #AIChatbots #CustomerSupport #WebsiteAutomation #AskZenAI",
    "With AskZen AI, your visitors get instant answers, and you get more time to focus on what matters. 🚀 #AIChatbot #CustomerSupport #Automation #AskZenAI",
  
    // Engagement Questions
    "💡 What's the one question your website visitors ask the most? Share below! #EngageWithAI #ChatbotSupport #AskZenAI",
    "If you had an AI assistant on your website, what’s the FIRST task you’d want it to handle? 🧠🤖 #AIChatbots #WebsiteAutomation #NoCode #AskZenAI",
    "Are you ready to say goodbye to 'Contact us for more info' on your website? We are! Follow us for updates. #AIRevolution #WebsiteChatbots #CustomerSupport #AskZenAI",
    "What’s more frustrating—an unanswered question or a long FAQ page? With AskZen AI, it’s neither. #WebsiteSupport #AIChatbot #NoCode #AskZenAI",
    "What feature do you wish every chatbot had? Let us know! 🚀 #Chatbots #AIForWeb #WebsiteAutomation #AskZenAI",
  
    // Countdown Posts
    "⏳ 30 days to go before AskZen AI changes the way your website answers questions! Follow us for the big reveal. 🎉 #AIChatbot #LaunchCountdown #TechInnovation #AskZenAI",
    "📆 The wait is almost over! AskZen AI launches in 25 days. A smarter, faster way to engage visitors. 🚀 #AI #LaunchSoon #CustomerSupport #AskZenAI",
    "🛠️ Final touches in progress! We’re 7 days away from launch. Get ready for smarter conversations on your website. #AskZenAI #TechLaunch #WebsiteSupport",
    "Only 3 weeks left until AskZen AI launches! Transform the way your website communicates. #AIChatbots #LaunchSoon #CustomerSupport #AskZenAI",
    "AskZen AI goes live in just 3 days! Ready to revolutionize your website’s support? 🚀 #TechInnovation #AIChatbot #LaunchCountdown #AskZenAI",
  
    // Fun and Relatable AI/Tech Content
    "🤖 Chatbots aren’t the future—they’re the NOW. Join the revolution with AskZen AI, launching soon! #AIRevolution #NoCodeSolutions #AskZenAI",
    "AskZen AI: because even your website deserves a smarter assistant. 😉 #Chatbots #AIForWeb #Innovation #AskZenAI",
    "AI isn’t here to take your job—it’s here to take your FAQ. Launching AskZen AI soon! #AIChatbot #WebsiteSupport #CustomerSupport #AskZenAI",
    "Fact: 60% of customers leave websites due to unanswered questions. Solution? AskZen AI. 🚀 #AIChatbots #CustomerSupport #WebsiteAutomation #AskZenAI",
    "Is your website ready for a smarter, faster future? AskZen AI launches soon! 💡 #AIChatbots #WebSupport #CustomerEngagement #AskZenAI",
  ];
  
  
  
  // Fill up to 50 tweets with excitement
  while (excitingTweets.length < 50) {
    excitingTweets.push("Fact: 60% of customers leave websites due to unanswered questions. Solution? AskZen AI. 🚀 #AIChatbots #CustomerSupport #WebsiteAutomation🚀");
  }
  
  // You can access the excitingTweets array to use in your tweets.
  // console.log(excitingTweets);  // Example: Display the exciting tweets array
  
  export { excitingTweets };
  
  