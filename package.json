{"name": "riona-ai-agent", "version": "1.0.0", "main": "src/index.ts", "scripts": {"start": "npx tsc && npm run postbuild && node build/index.js", "postbuild": "copyfiles -u 3 src/Agent/characters/*.json build/Agent/characters", "train:link": "tsc && node build/Agent/training/WebsiteScraping.js", "train:audio": "tsc && node build/Agent/training/TrainWithAudio.js", "train:youtube": "tsc && node build/Agent/training/youtubeURL.js"}, "keywords": [], "author": "", "license": "ISC", "description": "Riona-AI-Agent is an AI-powered automation tool designed to interact with various social media platforms.", "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.7.9", "cookie-parser": "^1.4.6", "copyfiles": "^2.4.1", "cors": "^2.8.5", "cron": "^3.5.0", "csv-parser": "^3.1.0", "dompurify": "^3.2.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.17.3", "helmet": "^8.0.0", "instagram-private-api": "^1.46.1", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.0", "mammoth": "^1.9.0", "moment": "^2.30.1", "mongoose": "^8.9.4", "node-cron": "^3.0.3", "pdf-parse": "^1.1.1", "playwright": "^1.49.1", "proxy-chain": "^2.5.6", "puppeteer": "^24.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "readline-sync": "^1.4.10", "request": "^2.88.2", "request-promise": "^4.2.6", "textract": "^2.5.0", "twitter-api-v2": "^1.19.0", "typescript": "^5.7.3", "user-agents": "^1.1.416", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "youtube-transcript": "^1.2.1"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.19", "@types/express": "^4.17.0", "@types/express-session": "^1.18.2", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.10", "@types/mime-types": "^2.1.4", "@types/pdf-parse": "^1.1.4", "@types/puppeteer": "^5.4.7", "@types/readline-sync": "^1.4.8", "@types/request": "^2.48.12", "@types/textract": "^2.4.5", "@types/user-agents": "^1.0.4"}}